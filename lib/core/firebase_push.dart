import 'dart:convert';
import 'dart:math';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:gotcha_mfg_app/config/router/app_router.dart';
import 'package:gotcha_mfg_app/core/utils/device_info.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/firebase_options.dart';
import 'package:gotcha_mfg_app/locator.dart';

import '../config/router/app_router.gr.dart';
import '../features/auth/data/models/fcm_req_model.dart';
import '../features/auth/presentation/blocs/fcm_update/fcm_update_cubit.dart';
import 'mixpanel_service.dart';
import 'utils/app_print.dart';

// Top-level function for background message handling
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  info("Handling a background message: ${message.messageId}");
  info('Message data: ${message.data}');
  info(
      'Message data: ${message.data}----${message.notification?.body}----${message.notification?.title}--}');

  // Track notification delivery for background state
  if (message.data.containsKey('type')) {
    final notificationType = message.data['type'];
    sl<MixpanelService>().trackPushNotificationDelivery(
      notificationType,
      'Background',
    );
  }
}

class MFGPushNotification {
  static FirebaseMessaging messaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // This app dont need this at app start
  // static Future<bool> requestPushPermissions() async {
  //   NotificationSettings settings = await messaging.requestPermission(
  //     alert: true,
  //     announcement: false,
  //     badge: true,
  //     carPlay: false,
  //     criticalAlert: false,
  //     provisional: false,
  //     sound: true,
  //   );
  //   return settings.authorizationStatus == AuthorizationStatus.authorized;
  // }

  static Future<String?> getToken() async {
    String? fcmToken = await messaging.getToken();
    info('>>>>Fallback iOS FCM token>>>>>>>>>>>>$fcmToken');
    return fcmToken;
  }

  static resetToken() async {
    await messaging.deleteToken();
  }

  static initListeners() async {
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    initializeLocalNotifications();

    // Handle terminated state: getInitialMessage
    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      // Store the notification data for later use after splash
      _pendingNotificationData = initialMessage.data;

      // Track notification tap for terminated state
      if (initialMessage.data.containsKey('type')) {
        final notificationType = initialMessage.data['type'];
        sl<MixpanelService>().trackPushNotificationTap(
          notificationType,
          'Terminated',
        );
      }
    }

    // Handle terminated state: onMessageOpenedApp
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      info('A new onMessageOpenedApp event was published!');
      _handleNotificationTap(message.data);
      info(
          'Message data: ${message.data}----${message.notification?.body}----${message.notification?.title}--}');

      // Track notification tap for background state
      if (message.data.containsKey('type')) {
        final notificationType = message.data['type'];
        sl<MixpanelService>().trackPushNotificationTap(
          notificationType,
          'Background',
        );
      }
    });

    // Handle foreground messages: onMessage
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      info('Got a message whilst in the foreground!');
      info(
          'Message data: ${message.data}----${message.notification?.body}----${message.notification?.title}--}');

      // Track notification delivery for foreground state
      if (message.data.containsKey('type')) {
        final notificationType = message.data['type'];
        sl<MixpanelService>().trackPushNotificationDelivery(
          notificationType,
          'Foreground',
        );
      }

      if (message.data.isNotEmpty) {
        if (!isIos) {
          createPush(
            title: message.notification?.title ?? "Hello",
            body: message.notification?.body ??
                "Thank you for downloading The Mental Fitness Gym.",
            payload: message.data,
          );
        }
      }
    });

    FirebaseMessaging.instance.onTokenRefresh.listen((fcmToken) {
      info('Refreshed token: $fcmToken');
      Future<String?> deviceId() async {
        return await getDeviceId();
      }

      Future<void> updateToken() async {
        try {
          final token = fcmToken;
          final device = await deviceId();
          sl<FcmUpdateCubit>().updateFcmToken(
            FcmRequestModel(
              token: token,
              fcmDeviceId: device ?? '',
            ),
          );
        } catch (e) {
          info('Error updating token: $e');
        }
      }

      updateToken();
    });
  }

  static initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
    ); // You can add delegate callbacks here if needed
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse:
            (NotificationResponse notificationResponse) async {
      if (notificationResponse.payload != null) {
        try {
          // Assuming the payload is a JSON string.  Adjust as needed.
          final Map<String, dynamic> payloadData =
              jsonDecode(notificationResponse.payload!);

          // Track notification tap for foreground notifications
          if (payloadData.containsKey('type')) {
            final notificationType = payloadData['type'];
            sl<MixpanelService>().trackPushNotificationTap(
              notificationType,
              'Foreground',
            );
          }

          _handleNotificationTap(payloadData);
        } catch (e) {
          info("Error parsing payload: $e");
        }
      }
    });
  }

  static void _handleNotificationTap(dynamic payload) {
    if (payload != null) {
      if (payload is Map<String, dynamic>) {
        if (payload.containsKey('on_tap_url')) {
          switch (payload['on_tap_url']) {
            case '/home':
              sl<AppRouter>().replaceAll(
                [HomeRoute(index: 0)],
                updateExistingRoutes: false,
              );
              break;
            case '/feature':
              sl<AppRouter>().replaceAll(
                [HomeRoute(index: 0)],
                updateExistingRoutes: false,
              );
              break;
            case '/profile':
              sl<AppRouter>().replaceAll(
                [HomeRoute(index: 2)],
                updateExistingRoutes: false,
              );
              break;
            case '/village':
              sl<AppRouter>().replaceAll(
                [
                  HomeRoute(index: 2),
                  const VillageHomeRoute(),
                ],
                updateExistingRoutes: false,
              );
              break;
            default:
              sl<AppRouter>().replaceAll(
                [HomeRoute(index: 0)],
                updateExistingRoutes: false,
              );
              return;
          }
        }
      }
    }
  }

  static createPush({
    required String title,
    required String body,
    Map<String, dynamic>? payload,
  }) async {
    const AndroidNotificationDetails androidNotificationDetailsGeneral =
        AndroidNotificationDetails(
      'com.gotcha4life.mentalfitnessgym.general_app_notifications',
      'General Notifications',
      channelDescription:
          'Notification channel for general app updates, news, and announcements.',
      importance: Importance.high,
      priority: Priority.high,
      ticker: 'Gotcha4Life',
    );

    late AndroidNotificationDetails androidPlatformChannelSpecifics;

    androidPlatformChannelSpecifics = androidNotificationDetailsGeneral;

    NotificationDetails notificationDetails = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: const DarwinNotificationDetails(
        presentAlert: false,
        presentBadge: false,
        presentSound: false,
      ),
    );

    String? payloadString;
    if (payload != null) {
      payloadString = jsonEncode(payload);
    }
    await flutterLocalNotificationsPlugin.show(
      Random().nextInt(9999),
      title,
      body,
      notificationDetails,
      payload: payloadString,
    );
  }

  // Add at class level
  static Map<String, dynamic>? _pendingNotificationData;

  static void handlePendingNotification() {
    if (_pendingNotificationData != null) {
      _handleNotificationTap(_pendingNotificationData);
      _pendingNotificationData = null;
    }
  }

  // Add method to check for pending notifications
  static bool hasPendingNotifications() {
    return _pendingNotificationData != null;
  }
}
